import com.coocaa.crypto.aes.CryptoAesUtil;
import com.coocaa.crypto.rsa.CryptoRsaUtil;
import com.coocaa.crypto.rsa.RSAKey;
import com.coocaa.crypto.CryptoMixedUtil;
import com.coocaa.crypto.AfterEncryption;
import com.coocaa.crypto.BeforeEncryption;

public class CryptoTest {
    
    public static void main(String[] args) {
        System.out.println("Starting crypto tests...");
        
        try {
            testAesEncryptDecrypt();
            testRsaEncryptDecrypt();
            testMixedEncryptDecrypt();
            System.out.println("All tests passed!");
        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void testAesEncryptDecrypt() throws Exception {
        System.out.println("Testing AES encryption/decryption...");
        
        String key = "1234567890123456"; // 16 bytes key
        String text = "Hello, AES!";
        
        String encrypted = CryptoAesUtil.encrypt(text, key);
        String decrypted = CryptoAesUtil.decrypt(encrypted, key);
        
        if (!text.equals(decrypted)) {
            throw new RuntimeException("AES test failed: expected '" + text + "', got '" + decrypted + "'");
        }
        
        System.out.println("AES test passed!");
    }
    
    public static void testRsaEncryptDecrypt() throws Exception {
        System.out.println("Testing RSA encryption/decryption...");
        
        RSAKey rsaKey = CryptoRsaUtil.generateRSAKey();
        String text = "Hello, RSA!";
        
        String encrypted = CryptoRsaUtil.encrypt(text, rsaKey.getPublicKey());
        String decrypted = CryptoRsaUtil.decrypt(encrypted, rsaKey.getPrivateKey());
        
        if (!text.equals(decrypted)) {
            throw new RuntimeException("RSA test failed: expected '" + text + "', got '" + decrypted + "'");
        }
        
        System.out.println("RSA test passed!");
    }
    
    public static void testMixedEncryptDecrypt() throws Exception {
        System.out.println("Testing Mixed encryption/decryption...");
        
        RSAKey rsaKey = CryptoRsaUtil.generateRSAKey();
        String text = "Hello, Mixed Encryption!";
        
        AfterEncryption afterEncryption = CryptoMixedUtil.encrypt(text, rsaKey.getPublicKey());
        BeforeEncryption beforeEncryption = CryptoMixedUtil.decrypt(
            afterEncryption.getCipherText(),
            afterEncryption.getCipherAesKey(),
            rsaKey.getPrivateKey()
        );
        
        if (!text.equals(beforeEncryption.getText())) {
            throw new RuntimeException("Mixed test failed: expected '" + text + "', got '" + beforeEncryption.getText() + "'");
        }
        
        System.out.println("Mixed test passed!");
    }
}
