//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.coocaa.crypto;

import com.coocaa.crypto.aes.CryptoAesUtil;
import com.coocaa.crypto.rsa.CryptoRsaUtil;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

public class CryptoMixedUtil {
    private static final String CHARACTER_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int LENGTH = 16;
    private static final Integer KEY_LENGTH = 8;

    public CryptoMixedUtil() {
    }

    public static AfterEncryption encrypt(String text, String aesKey, String publicKey) throws InvalidAlgorithmParameterException, NoSuchPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        checkAseKey(aesKey);
        String encContent = CryptoAesUtil.encrypt(text, aesKey);
        String encKey = CryptoRsaUtil.encrypt(aesKey, publicKey);
        AfterEncryption afterEncryption = new AfterEncryption();
        afterEncryption.setCipherText(encContent);
        afterEncryption.setCipherAesKey(encKey);
        return afterEncryption;
    }

    public static AfterEncryption encrypt(String text, String publicKey) throws InvalidAlgorithmParameterException, NoSuchPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String aesKey = generateAesKey();
        String encContent = CryptoAesUtil.encrypt(text, aesKey);
        String encKey = CryptoRsaUtil.encrypt(aesKey, publicKey);
        AfterEncryption afterEncryption = new AfterEncryption();
        afterEncryption.setCipherText(encContent);
        afterEncryption.setCipherAesKey(encKey);
        return afterEncryption;
    }

    public static String generateAesKey() {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(16);

        for(int i = 0; i < 16; ++i) {
            int randomIndex = random.nextInt("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".length());
            char randomChar = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(randomIndex);
            sb.append(randomChar);
        }

        return sb.toString();
    }

    public static BeforeEncryption decrypt(String cipherText, String cipherAseKey, String privateKey) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, UnsupportedEncodingException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String decKey = CryptoRsaUtil.decrypt(cipherAseKey, privateKey);
        String decContent = CryptoAesUtil.decrypt(cipherText, decKey);
        BeforeEncryption beforeEncryption = new BeforeEncryption();
        beforeEncryption.setText(decContent);
        beforeEncryption.setAesKey(decKey);
        return beforeEncryption;
    }

    public static String decryptText(String cipherText, String cipherAesKey, String privateKey) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, UnsupportedEncodingException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String decKey = CryptoRsaUtil.decrypt(cipherAesKey, privateKey);
        String decContent = CryptoAesUtil.decrypt(cipherText, decKey);
        return decContent;
    }

    public static void checkAseKey(String aesKey) {
        if (aesKey.length() != 2 * KEY_LENGTH && aesKey.length() != 3 * KEY_LENGTH && aesKey.length() != 4 * KEY_LENGTH) {
            throw new RuntimeException("密钥的长度需为16/24/32字节");
        }
    }
}
