use rand::distributions::Alphanumeric;
use rand::{thread_rng, Rng};

use crate::aes;
use crate::error::CryptoError;
use crate::rsa;

#[derive(Debug)]
pub struct AfterEncryption {
    pub cipher_text: String,
    pub cipher_aes_key: String,
}

#[derive(Debug)]
pub struct BeforeEncryption {
    pub text: String,
    pub aes_key: String,
}

fn generate_aes_key() -> String {
    thread_rng()
        .sample_iter(&Alphanumeric)
        .take(16)
        .map(char::from)
        .collect()
}

pub fn encrypt(
    text: &str,
    public_key: &str,
) -> Result<AfterEncryption, CryptoError> {
    let aes_key = generate_aes_key();
    let enc_content = aes::encrypt(text, &aes_key)?;
    let enc_key = rsa::encrypt(&aes_key, public_key)?;

    Ok(AfterEncryption {
        cipher_text: enc_content,
        cipher_aes_key: enc_key,
    })
}

pub fn decrypt(
    cipher_text: &str,
    cipher_aes_key: &str,
    private_key: &str,
) -> Result<BeforeEncryption, CryptoError> {
    let dec_key = rsa::decrypt(cipher_aes_key, private_key)?;
    let dec_content = aes::decrypt(cipher_text, &dec_key)?;

    Ok(BeforeEncryption {
        text: dec_content,
        aes_key: dec_key,
    })
}
