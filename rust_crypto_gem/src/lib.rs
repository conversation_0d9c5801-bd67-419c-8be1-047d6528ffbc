pub mod aes;
pub mod error;
pub mod mixed;
pub mod rsa;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_aes_encrypt_decrypt() {
        let key = "1234567890123456";
        let text = "Hello, AES!";

        let encrypted = aes::encrypt(text, key).unwrap();
        let decrypted = aes::decrypt(&encrypted, key).unwrap();

        assert_eq!(text, decrypted);
    }

    #[test]
    fn test_rsa_encrypt_decrypt() {
        let rsa_key = rsa::generate_rsa_key().unwrap();
        let text = "Hello, RSA!";

        let encrypted = rsa::encrypt(text, &rsa_key.public_key).unwrap();
        let decrypted = rsa::decrypt(&encrypted, &rsa_key.private_key).unwrap();

        assert_eq!(text, decrypted);
    }

    #[test]
    fn test_mixed_encrypt_decrypt() {
        let rsa_key = rsa::generate_rsa_key().unwrap();
        let text = "Hello, Mixed Encryption!";

        let after_encryption = mixed::encrypt(text, &rsa_key.public_key).unwrap();
        let before_encryption = mixed::decrypt(
            &after_encryption.cipher_text,
            &after_encryption.cipher_aes_key,
            &rsa_key.private_key,
        )
        .unwrap();

        assert_eq!(text, before_encryption.text);
    }
}