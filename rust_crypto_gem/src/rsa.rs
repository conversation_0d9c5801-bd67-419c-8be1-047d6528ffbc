use base64::{engine::general_purpose, Engine as _};
use rsa::pkcs8::{DecodePrivate<PERSON><PERSON>, DecodeP<PERSON>licKey, EncodePrivate<PERSON><PERSON>, EncodePublicKey, LineEnding};
use rsa::{Pkcs1v15Encrypt, RsaPrivate<PERSON>ey, RsaPublicKey};

use crate::error::CryptoError;

pub struct RsaKey {
    pub public_key: String,
    pub private_key: String,
}

pub fn generate_rsa_key() -> Result<RsaKey, CryptoError> {
    let mut rng = rand::thread_rng();
    let bits = 2048;
    let private_key = RsaPrivateKey::new(&mut rng, bits)
        .map_err(|e| CryptoError::RsaKeyGenerationError(e.to_string()))?;
    let public_key = RsaPublicKey::from(&private_key);

    let private_key_pem = private_key
        .to_pkcs8_pem(LineEnding::LF)
        .map_err(|e| CryptoError::RsaKeyGenerationError(e.to_string()))?;
    let public_key_pem = public_key
        .to_public_key_pem(LineEnding::LF)
        .map_err(|e| CryptoError::RsaKeyGenerationError(e.to_string()))?;

    Ok(RsaKey {
        public_key: public_key_pem.to_string(),
        private_key: private_key_pem.to_string(),
    })
}

pub fn encrypt(text: &str, public_key_str: &str) -> Result<String, CryptoError> {
    let public_key = RsaPublicKey::from_public_key_pem(public_key_str)
        .map_err(|_| CryptoError::InvalidPublicKey)?;

    let mut rng = rand::thread_rng();
    let encrypted_bytes = public_key
        .encrypt(&mut rng, Pkcs1v15Encrypt, text.as_bytes())
        .map_err(|e| CryptoError::RsaError(e.to_string()))?;

    Ok(general_purpose::STANDARD.encode(&encrypted_bytes))
}

pub fn decrypt(secret_text: &str, private_key_str: &str) -> Result<String, CryptoError> {
    let private_key = RsaPrivateKey::from_pkcs8_pem(private_key_str)
        .map_err(|_| CryptoError::InvalidPrivateKey)?;

    let secret_text_decoded = general_purpose::STANDARD.decode(secret_text)?;

    let decrypted_bytes = private_key
        .decrypt(Pkcs1v15Encrypt, &secret_text_decoded)
        .map_err(|e| CryptoError::RsaError(e.to_string()))?;

    Ok(String::from_utf8(decrypted_bytes)?)
}
