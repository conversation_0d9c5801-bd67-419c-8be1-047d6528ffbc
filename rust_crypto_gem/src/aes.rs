use cbc::cipher::{BlockDecryptMut, BlockEncryptMut, KeyIvInit};
use cbc::cipher::block_padding::Pkcs7;
use hex::{decode, encode};

use crate::error::CryptoError;

type Aes128CbcEnc = cbc::Encryptor<aes::Aes128>;
type Aes128CbcDec = cbc::Decryptor<aes::Aes128>;

const FILL_VECTOR: &[u8] = b"1234567890abcdef";

pub fn encrypt(content: &str, aes_sec: &str) -> Result<String, CryptoError> {
    let key_len = aes_sec.as_bytes().len();
    if key_len != 16 {
        return Err(CryptoError::InvalidKeyLength(key_len));
    }

    let key = aes_sec.as_bytes();
    let iv = FILL_VECTOR;

    let cipher = Aes128CbcEnc::new_from_slices(key, iv)
        .map_err(|e| CryptoError::AesError(e.to_string()))?;

    let ciphertext = cipher.encrypt_padded_vec_mut::<Pkcs7>(content.as_bytes());
    Ok(encode(ciphertext).to_uppercase())
}

pub fn decrypt(content: &str, key: &str) -> Result<String, CryptoError> {
    let key_bytes = key.as_bytes();
    let iv = FILL_VECTOR;

    let cipher = Aes128CbcDec::new_from_slices(key_bytes, iv)
        .map_err(|e| CryptoError::AesError(e.to_string()))?;

    let encrypted_bytes = decode(content.to_lowercase())?;
    let decrypted_bytes = cipher
        .decrypt_padded_vec_mut::<Pkcs7>(&encrypted_bytes)
        .map_err(|e| CryptoError::AesError(e.to_string()))?;

    Ok(String::from_utf8(decrypted_bytes)?)
}
