use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, Debug)]
pub enum CryptoError {
    #[error("Invalid key length: {0}")]
    InvalidKeyLength(usize),

    #[error("AES encryption/decryption error: {0}")]
    A<PERSON><PERSON>rror(String),

    #[error("RSA encryption/decryption error: {0}")]
    RsaError(String),

    #[error("Hex decoding error: {0}")]
    HexError(#[from] hex::FromHexError),

    #[error("Base64 decoding error: {0}")]
    Base64Error(#[from] base64::DecodeError),

    #[error("UTF-8 conversion error: {0}")]
    Utf8Error(#[from] std::string::FromUtf8Error),

    #[error("RSA key generation error: {0}")]
    RsaKeyGenerationError(String),

    #[error("Invalid public key")]
    InvalidPublicKey,

    #[error("Invalid private key")]
    InvalidPrivate<PERSON>ey,
}
